name: audioplayers_platform_interface
resolution: workspace
description: The platform interface for audioplayers, a Flutter plugin to play multiple audio files simultaneously
version: 7.1.1
homepage: https://github.com/bluefireteam/audioplayers
repository: https://github.com/bluefireteam/audioplayers/tree/master/packages/audioplayers_platform_interface

dependencies:
  collection: ^1.18.0
  flutter:
    sdk: flutter
  meta: ^1.12.0
  plugin_platform_interface: ^2.1.8

dev_dependencies:
  flame_lint: ^1.4.1
  flutter_test:
    sdk: flutter

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"
