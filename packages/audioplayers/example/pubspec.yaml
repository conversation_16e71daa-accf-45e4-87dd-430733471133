name: audioplayers_example
resolution: workspace
description: Demonstrates how to use the audioplayers plugin.
publish_to: none

dependencies:
  audioplayers:
    path: ../
  collection: ^1.18.0
  file_picker: ^10.3.3
  flutter:
    sdk: flutter
  http: ^1.5.0
  path_provider: ^2.1.5
  provider: ^6.1.5+1

dev_dependencies:
  # Integration tests for audioplayers_platform_interface are handled 
  # in this package to avoid maintaining multiple example apps:
  audioplayers_platform_interface:
    path: ../../audioplayers_platform_interface
  flame_lint: ^1.4.2
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"
