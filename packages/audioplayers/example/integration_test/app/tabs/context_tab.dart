import 'package:flutter_test/flutter_test.dart';

import '../../platform_features.dart';
import '../../test_utils.dart';
import '../app_source_test_data.dart';

Future<void> testContextTab(
  WidgetTester tester,
  AppSourceTestData audioSourceTestData,
  PlatformFeatures features,
) async {
  printWithTimeOnFailure('Test Context Tab');
  // Audio context
  // TODO(Gustl22): test generic flags
  // await tester.tap(find.byKey(const Key('audioContextTab')));
  // await tester.pumpAndSettle();
}
