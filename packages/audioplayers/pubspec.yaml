name: audioplayers
description: A Flutter plugin to play multiple audio files simultaneously
version: 6.5.1
homepage: https://github.com/bluefireteam/audioplayers
repository: https://github.com/bluefireteam/audioplayers/tree/master/packages/audioplayers

flutter:
  plugin:
    platforms:
      android:
        default_package: audioplayers_android
      ios:
        default_package: audioplayers_darwin
      linux:
        default_package: audioplayers_linux
      macos:
        default_package: audioplayers_darwin
      web:
        default_package: audioplayers_web
      windows:
        default_package: audioplayers_windows
      ohos:
        default_package: audioplayers_ohos

dependencies:
  audioplayers_android:
    path: ../audioplayers_android
  audioplayers_darwin:
    path: ../audioplayers_darwin
  audioplayers_linux:
    path: ../audioplayers_linux
  audioplayers_platform_interface:
    path: ../audioplayers_platform_interface
  audioplayers_web:
    path: ../audioplayers_web
  audioplayers_windows:
    path: ../audioplayers_windows
  audioplayers_ohos:
    path: ../audioplayers_ohos
  file: ^7.0.1
  flutter:
    sdk: flutter
  http: ^1.5.0
  meta: ^1.12.0
  path_provider: ^2.1.5
  synchronized: ^3.1.0+1
  uuid: ^4.5.1

dev_dependencies:
  lints: ^4.0.0
  flutter_test:
    sdk: flutter

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"

topics:
  - audio
  - audio-player
