name: audioplayers_web
resolution: workspace
description: Web implementation of audioplayers, a Flutter plugin to play multiple audio files simultaneously
version: 5.1.1
homepage: https://github.com/bluefireteam/audioplayers
repository: https://github.com/bluefireteam/audioplayers/tree/master/packages/audioplayers_web

flutter:
  plugin:
    platforms:
      web:
        pluginClass: AudioplayersPlugin
        fileName: audioplayers_web.dart

dependencies:
  audioplayers_platform_interface:
    path: ../audioplayers_platform_interface
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  web: ">=0.5.1 <2.0.0"

dev_dependencies:
  flame_lint: ^1.4.2
  flutter_test:
    sdk: flutter

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"
