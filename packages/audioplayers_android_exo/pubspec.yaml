name: audioplayers_android_exo
resolution: workspace
description: Android implementation of audioplayers, a Flutter plugin to play multiple audio files simultaneously
version: 0.1.2+1
homepage: https://github.com/bluefireteam/audioplayers
repository: https://github.com/bluefireteam/audioplayers/tree/master/packages/audioplayers_android_exo

flutter:
  plugin:
    implements: audioplayers
    platforms:
      android:
        package: xyz.luan.audioplayers
        pluginClass: AudioplayersPlugin

dependencies:
  audioplayers_platform_interface:
    path: ../audioplayers_platform_interface
  flutter:
    sdk: flutter

dev_dependencies:
  flame_lint: ^1.4.2
  flutter_test:
    sdk: flutter

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"
