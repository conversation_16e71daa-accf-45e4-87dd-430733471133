group 'xyz.luan.audioplayers'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.7.10'
    ext.coroutines_version = '1.6.4'

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "de.mannodermaus.gradle.plugins:android-junit5:*******"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'de.mannodermaus.android-junit5'

android {
    compileSdk 35

    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty('namespace')) {
        namespace 'xyz.luan.audioplayers'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lint {
        disable 'InvalidPackage'
    }
}

allprojects {
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
        }
    }
}

dependencies {
    def exoplayer_version = "1.5.1"
    implementation "androidx.media3:media3-exoplayer:${exoplayer_version}"
    implementation "androidx.media3:media3-exoplayer-hls:${exoplayer_version}"
    implementation "androidx.media3:media3-exoplayer-dash:${exoplayer_version}"
    implementation "androidx.media3:media3-exoplayer-rtsp:${exoplayer_version}"
    implementation "androidx.media3:media3-exoplayer-smoothstreaming:${exoplayer_version}"

    implementation "androidx.core:core-ktx:1.9.0" // Do not pump unless dropping support for AGP7
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutines_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines_version"
    testImplementation 'org.junit.jupiter:junit-jupiter:5.9.0'
    testImplementation 'org.assertj:assertj-core:3.23.1'
}
repositories {
    mavenCentral()
}
