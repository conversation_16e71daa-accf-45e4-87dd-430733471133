<p align="center">
  <a href="https://pub.dev/packages/audioplayers">
    <img alt="AudioPlayers" height="150px" src="https://raw.githubusercontent.com/bluefireteam/audioplayers/main/images/logo_ap_compact.svg">
  </a>
</p>

---

# audioplayers_windows
<p>
  <a title="Pub" href="https://pub.dev/packages/audioplayers_windows"><img src="https://img.shields.io/pub/v/audioplayers_windows.svg?style=popout&include_prereleases"/></a>
  <a title="Build Status" href="https://github.com/bluefireteam/audioplayers/actions?query=workflow%3Abuild+branch%3Amain"><img src="https://github.com/bluefireteam/audioplayers/actions/workflows/build.yml/badge.svg?branch=main"/></a>
  <a title="Discord" href="https://discord.gg/pxrBmy4"><img src="https://img.shields.io/discord/509714518008528896.svg"/></a>
  <a title="Melos" href="https://github.com/invertase/melos"><img src="https://img.shields.io/badge/maintained%20with-melos-f700ff.svg"/></a>
</p>

The Windows implementation of [`audioplayers`](https://pub.dev/packages/audioplayers).

## Usage

This package is [endorsed](https://flutter.dev/docs/development/packages-and-plugins/developing-packages#endorsed-federated-plugin),
which means you can simply use `audioplayers` normally. 
This package will be automatically included in your app when you do, so you do not need to add it to your `pubspec.yaml`.

## Setup for Windows

Please follow the Flutter guide to [set up Flutter on Windows](https://docs.flutter.dev/get-started/install/windows#windows-setup).

Optionally you can add the individual component `NuGet package manager` inside **Visual Studio** or **Visual Studio Build Tools**, otherwise it will be downloaded while building.
