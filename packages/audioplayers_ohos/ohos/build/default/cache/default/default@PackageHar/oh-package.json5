{"name": "audioplayers_ohos", "version": "1.0.0", "license": "MIT", "description": "OpenHarmony implementation of audioplayers.", "dependencies": {"@ohos/flutter_ohos": "file:../../../../../../../testflutter/ohos/har/flutter.har"}, "types": "./src/main/ets/index.d.ets", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": false, "dependencyPkgVersion": {"@ohos/flutter_ohos": "1.0.0-2317265cd5"}, "declarationEntry": [], "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 13, "compatibleSdkType": "HarmonyOS", "obfuscated": false}