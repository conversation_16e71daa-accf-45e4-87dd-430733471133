{"resolveConflictMode": true, "depName2RootPath": {"@ohos/flutter_ohos": "/Users/<USER>/testflutter/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@jl6us5hj+2s0atxhhapiezj9pzy1xit9cdjv9tfn5q0=/oh_modules/@ohos/flutter_ohos", "@ohos/hypium": "/Users/<USER>/testflutter/ohos/oh_modules/.ohpm/@ohos+hypium@1.0.6/oh_modules/@ohos/hypium"}, "depName2DepInfo": {"audioplayers_ohos": {"dependencyType": "har", "isByteCodeHar": false, "pkgRootPath": "/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos", "pkgName": "audioplayers_ohos", "pkgVersion": "1.0.0"}, "@ohos/flutter_ohos": {"dependencyType": "har", "isByteCodeHar": false, "pkgRootPath": "/Users/<USER>/testflutter/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@jl6us5hj+2s0atxhhapiezj9pzy1xit9cdjv9tfn5q0=/oh_modules/@ohos/flutter_ohos", "pkgName": "@ohos/flutter_ohos", "pkgVersion": "1.0.0-2317265cd5"}, "@ohos/hypium": {"dependencyType": "other", "isByteCodeHar": false, "pkgRootPath": "/Users/<USER>/testflutter/ohos/oh_modules/.ohpm/@ohos+hypium@1.0.6/oh_modules/@ohos/hypium", "pkgName": "@ohos/hypium", "pkgVersion": "1.0.6"}}}