{"configPath": "/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos/build/default/intermediates/process_profile/default/module.json", "packageName": "cn.pan2017.y<PERSON><PERSON><PERSON><PERSON>", "output": "/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos/build/default/intermediates/res/default", "moduleNames": "audioplayers_ohos,entry,stockfish_chess_engine,fluttertoast,shared_preferences_ohos", "ResourceTable": ["/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos/build/default/generated/r/default/ResourceTable.h"], "moduleResources": ["/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos/src/main/resources"], "dependencies": ["/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos/build/default/intermediates/res/default/resource_str"], "iconCheck": false, "compression": "/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos/build/default/intermediates/res/default/opt-compression.json", "ids": "/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos/build/default/intermediates/res/default/ids_map", "definedIds": "/Users/<USER>/code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos/build/default/intermediates/res/default/ids_map/id_defined.json", "definedSysIds": "/Applications/DevEco-Studio.app/Contents/sdk/default/hms/toolchains/id_defined.json"}