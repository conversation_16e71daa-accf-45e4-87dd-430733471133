{"app": {"bundleName": "cn.pan2017.y<PERSON><PERSON><PERSON><PERSON>", "debug": false, "versionCode": 14, "versionName": "0.1.6", "minAPIVersion": 50001013, "targetAPIVersion": 50101019, "apiReleaseType": "Release", "compileSdkVersion": "*********", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "release"}, "module": {"name": "audioplayers_ohos", "type": "har", "description": "OpenHarmony implementation of audioplayers plugin", "deviceTypes": ["phone", "tablet", "2in1"], "installationFree": false, "abilities": [], "extensionAbilities": [], "requestPermissions": [{"name": "ohos.permission.INTERNET", "reason": "$string:internet_permission_reason", "usedScene": {"abilities": [], "when": "inuse"}}, {"name": "ohos.permission.READ_MEDIA", "reason": "$string:read_media_permission_reason", "usedScene": {"abilities": [], "when": "inuse"}}], "packageName": "audioplayers_ohos"}}