name: audioplayers_darwin
resolution: workspace
description: iOS and macOS implementation of audioplayers, a Flutter plugin to play multiple audio files simultaneously
version: 6.3.0
homepage: https://github.com/bluefireteam/audioplayers
repository: https://github.com/bluefireteam/audioplayers/tree/master/packages/audioplayers_darwin

flutter:
  plugin:
    implements: audioplayers
    platforms:
      ios:
        pluginClass: AudioplayersDarwinPlugin
        sharedDarwinSource: true
      macos:
        pluginClass: AudioplayersDarwinPlugin
        sharedDarwinSource: true

dependencies:
  audioplayers_platform_interface:
    path: ../audioplayers_platform_interface
  flutter:
    sdk: flutter

dev_dependencies:
  flame_lint: ^1.4.2
  flutter_test:
    sdk: flutter

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"
