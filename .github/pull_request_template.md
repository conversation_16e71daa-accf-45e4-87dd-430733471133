# Description

<!-- Provide a description of what this PR is doing. 
If you're modifying existing behavior, describe the existing behavior, how this PR is changing it,
and what motivated the change. If this is a breaking change, specify explicitly which APIs have been
changed. -->

## Checklist

<!-- Before you create this PR confirm that it meets all requirements listed below by checking the
relevant checkboxes (`[x]`). This will ensure a smooth and quick review process. -->

- [ ] The title of my PR starts with a [Conventional Commit] prefix (`fix:`, `feat:`, `refactor:`,
      `docs:`, `chore:`, `test:`, `ci:` etc).
- [ ] I have read the [Contributor Guide] and followed the process outlined for submitting PRs.
- [ ] I have updated/added tests for ALL new/updated/fixed functionality.
- [ ] I have updated/added relevant documentation and added dartdoc comments with `///`, where necessary.
- [ ] I have updated/added relevant examples in [example].

## Breaking Change

<!-- Does your PR require audioplayers users to manually update their apps to accommodate your change? 

If the PR is a breaking change this should be indicated with suffix "!" 
(for example, `feat!:`, `fix!:`). See [Conventional Commit] for details. -->

- [ ] Yes, this is a breaking change.
- [ ] No, this is *not* a breaking change.

<!-- If the PR is breaking, uncomment the following section and add instructions for how to migrate from
the currently released version to the new proposed way. -->

<!--
### Migration instructions

Before:
```
```

After:
```
```
-->

## Related Issues

<!-- Provide a list of issues related to this PR from the [issue database].
Indicate which of these issues are resolved or fixed by this PR, i.e. Fixes #xxx !-->

<!-- Links -->
[issue database]: https://github.com/bluefireteam/audioplayers/issues
[Contributor Guide]: https://github.com/bluefireteam/audioplayers/blob/main/contributing.md#feature-requests--prs
[Conventional Commit]: https://conventionalcommits.org
[example]: https://github.com/bluefireteam/audioplayers/tree/main/packages/audioplayers/example
