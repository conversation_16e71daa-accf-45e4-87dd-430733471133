name: Prepare release
on:
  workflow_dispatch:
    inputs:
      prerelease:
        description: 'Version as prerelease'
        required: false
        default: false
        type: boolean

jobs:
  prepare-release:
    name: Prepare release
    permissions:
      contents: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: subosito/flutter-action@v2
      - uses: bluefireteam/melos-action@v3
        with:
          run-versioning: ${{ inputs.prerelease == false }}
          run-versioning-prerelease: ${{ inputs.prerelease == true }}
          publish-dry-run: true
          create-pr: true
