name: build
on:
  push:
    branches:
      - main
  workflow_dispatch:

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false
  
jobs:
  call-build-example:
    uses: ./.github/workflows/build-example.yml
    with:
      upload_pages_artifact: true
  
  call-test:
    needs: call-build-example
    uses: ./.github/workflows/test.yml
    with:
      enable_min_version: true
  
  deploy-github-pages:
    needs: 
      - call-build-example
      - call-test
    permissions:
      pages: write
      id-token: write
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
