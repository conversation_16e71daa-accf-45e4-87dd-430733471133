name: build example
on:
  workflow_dispatch:
    inputs:
      flutter_version:
        description: 'Flutter Version'
        required: false
        default: 'any'
        type: choice
        options:
          - 'any'
          - '3.32.x'
          - '3.29.x'
          - '3.27.x'
      flutter_channel:
        description: 'Flutter Channel'
        required: false
        default: 'stable'
        type: choice
        options:
          - 'stable'
          - 'beta'
          - 'dev'
          - 'master'
      enable_android:
        description: 'Build Android'
        required: false
        default: true
        type: boolean
      enable_web:
        description: 'Build Web'
        required: false
        default: true
        type: boolean
      enable_ios:
        description: 'Build IOS'
        required: false
        default: true
        type: boolean
      enable_windows:
        description: 'Build Windows'
        required: false
        default: true
        type: boolean
      enable_linux:
        description: 'Build Linux'
        required: false
        default: true
        type: boolean
      enable_macos:
        description: 'Build MacOS'
        required: false
        default: true
        type: boolean
      upload_pages_artifact:
        description: 'Upload build artifact for GH pages'
        required: false
        default: false
        type: boolean
  workflow_call:
    inputs:
      flutter_version:
        required: false
        default: '3.32.4'
        type: string
      flutter_channel:
        required: false
        default: 'stable'
        type: string
      enable_android:
        required: false
        default: true
        type: boolean
      enable_web:
        required: false
        default: true
        type: boolean
      enable_ios:
        required: false
        default: true
        type: boolean
      enable_windows:
        required: false
        default: true
        type: boolean
      enable_linux:
        required: false
        default: true
        type: boolean
      enable_macos:
        required: false
        default: true
        type: boolean
      upload_pages_artifact:
        required: false
        default: false
        type: boolean

jobs:
  web:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: inputs.enable_web
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ inputs.flutter_version }}
          channel: ${{ inputs.flutter_channel }}
      - uses: bluefireteam/melos-action@v3

      - name: Example app - Build Web app
        working-directory: ./packages/audioplayers/example
        run: flutter build web --base-href "/audioplayers/"
      - name: Example app - Build Web app in WASM
        working-directory: ./packages/audioplayers/example
        run: flutter build web --base-href "/audioplayers/" --wasm
      - name: Upload pages artifact
        if: inputs.upload_pages_artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./packages/audioplayers/example/build/web

  android:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    if: inputs.enable_android
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ inputs.flutter_version }}
          channel: ${{ inputs.flutter_channel }}
      - uses: bluefireteam/melos-action@v3

      - name: Example App - Build Android APK
        working-directory: ./packages/audioplayers/example
        run: flutter build apk --release

  ios:
    runs-on: macos-14
    timeout-minutes: 30
    if: inputs.enable_ios
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ inputs.flutter_version }}
          channel: ${{ inputs.flutter_channel }}
      - uses: bluefireteam/melos-action@v3

      - name: Example app - Build iOS
        working-directory: ./packages/audioplayers/example
        run: flutter build ios --release --no-codesign

  macos:
    runs-on: macos-14
    timeout-minutes: 30
    if: inputs.enable_macos
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ inputs.flutter_version }}
          channel: ${{ inputs.flutter_channel }}
      - uses: bluefireteam/melos-action@v3

      - name: Example app - Build macOS
        working-directory: ./packages/audioplayers/example
        run: flutter build macos --release

  windows:
    runs-on: windows-latest
    timeout-minutes: 30
    if: inputs.enable_windows
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ inputs.flutter_version }}
          channel: ${{ inputs.flutter_channel }}
      - uses: bluefireteam/melos-action@v3

      - name: Example app - Build Windows app
        working-directory: ./packages/audioplayers/example
        run: flutter build windows --release

  linux:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: inputs.enable_linux
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ inputs.flutter_version }}
          channel: ${{ inputs.flutter_channel }}
      - uses: bluefireteam/melos-action@v3

      - name: Install Flutter requirements for Linux
        run: |
          sudo apt-get update
          sudo apt-get install -y clang cmake ninja-build pkg-config libgtk-3-dev liblzma-dev
      - name: Install GStreamer
        # Install libunwind-dev, see https://github.com/actions/runner-images/issues/6399#issuecomment-1285011525
        run: |
          sudo apt install -y libunwind-dev
          sudo apt-get install -y libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev

      - name: Example app - Build Linux app
        working-directory: ./packages/audioplayers/example
        run: flutter build linux --release
