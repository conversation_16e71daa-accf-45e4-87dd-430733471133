name: Tag and start release
on:
  push:
    branches: [main]

jobs:
  publish-packages:
    name: Create tags for release
    permissions:
      actions: write
      contents: write
    runs-on: [ ubuntu-latest ]
    if: contains(github.event.head_commit.message, 'chore(release)')
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: subosito/flutter-action@v2
      - uses: bluefireteam/melos-action@v3
        with:
          tag: true
      - run: |
          melos exec -c 1 --no-published --no-private --order-dependents -- \
          gh workflow run release-publish.yml \
          --ref \$MELOS_PACKAGE_NAME-v\$MELOS_PACKAGE_VERSION
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

