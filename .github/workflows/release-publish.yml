name: Publish packages
on:
  workflow_dispatch:

jobs:
  publish-packages:
    name: Publish packages
    permissions:
      contents: write
      id-token: write # Required for authentication using OIDC
    runs-on: [ ubuntu-latest ]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: subosito/flutter-action@v2
      - uses: bluefireteam/melos-action@v3
        with:
          publish: true
